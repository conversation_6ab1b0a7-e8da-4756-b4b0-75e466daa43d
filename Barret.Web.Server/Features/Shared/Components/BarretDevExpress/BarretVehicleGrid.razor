@using Radzen.Blazor
@using DevExpress.Blazor.Grid
@using Barret.Web.Server.Features.Vehicles.Data
@typeparam TItem

<RadzenDataGrid @ref="grid"
                Data="@Data"
                TItem="TItem"
                AllowFiltering="@ShowFilterRow"
                AllowPaging="@ShowPager"
                PageSize="@PageSize"
                RowSelect="@OnRowSelect"
                class="@($"barret-grid vehicle-list-grid {CssClass}")">
    <Columns>
        @ColumnsContent
    </Columns>
</RadzenDataGrid>

@code {
    private RadzenDataGrid<TItem>? grid;

    /// <summary>
    /// Gets or sets the data for the grid.
    /// </summary>
    [Parameter]
    public IEnumerable<TItem> Data { get; set; } = Enumerable.Empty<TItem>();

    /// <summary>
    /// Gets or sets the CSS class for the grid.
    /// </summary>
    [Parameter]
    public string CssClass { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets whether to show the filter row.
    /// </summary>
    [Parameter]
    public bool ShowFilterRow { get; set; } = false;

    /// <summary>
    /// Gets or sets whether to show the pager.
    /// </summary>
    [Parameter]
    public bool ShowPager { get; set; } = true;

    /// <summary>
    /// Gets or sets the page size.
    /// </summary>
    [Parameter]
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Gets or sets the columns content.
    /// </summary>
    [Parameter]
    public RenderFragment ColumnsContent { get; set; } = null!;

    /// <summary>
    /// Gets or sets the key field name (maintained for compatibility).
    /// </summary>
    [Parameter]
    public string? KeyFieldName { get; set; }

    /// <summary>
    /// Gets or sets the row click event handler.
    /// </summary>
    [Parameter]
    public Action<GridRowClickEventArgs>? RowClick { get; set; }

    /// <summary>
    /// Gets or sets the customize element event handler (maintained for compatibility).
    /// </summary>
    [Parameter]
    public Action<GridCustomizeElementEventArgs>? CustomizeElement { get; set; }

    /// <summary>
    /// Handles row selection in RadzenDataGrid.
    /// </summary>
    /// <param name="item">The selected item.</param>
    private void OnRowSelect(TItem item)
    {
        // For now, we'll disable the RowClick functionality during migration
        // Components using this wrapper should be updated to use RadzenDataGrid directly
        // TODO: Update consuming components to use RadzenDataGrid RowSelect event
    }

    /// <summary>
    /// Gets the RadzenDataGrid reference for external access.
    /// </summary>
    public RadzenDataGrid<TItem>? Grid => grid;
}
